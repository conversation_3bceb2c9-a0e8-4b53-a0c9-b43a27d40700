import { importProvidersFrom } from '@angular/core';
import { AppComponent } from './app/app.component';
import { environment } from './environments/environment';
import { StoreDevtoolsModule } from '@ngrx/store-devtools';
import { MatieresEffects } from './app/features/matieres/store/matieres.effects';
import { QuestionRepositoryEffects } from './app/features/questions-repository/store/questions-repository.effects';
import { TestManagementEffects } from './app/features/test-management/store/test-management.effects';
import { AuthEffects } from './app/features/auth/store/auth.effects';
import { EffectsModule } from '@ngrx/effects';
import { reducers } from './app/core/app.state';
import { StoreModule } from '@ngrx/store';
import { routes } from './app/app.routes';
import { bootstrapApplication, BrowserModule } from '@angular/platform-browser';
import { DATE_PIPE_DEFAULT_OPTIONS } from '@angular/common';
import { TokenInterceptor } from './app/core/interceptors/token.interceptor';
import {
  HTTP_INTERCEPTORS,
  provideHttpClient,
  withInterceptorsFromDi,
} from '@angular/common/http';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { provideRouter, withComponentInputBinding } from '@angular/router';

bootstrapApplication(AppComponent, {
  providers: [
    importProvidersFrom(
      BrowserModule,
      StoreModule.forRoot(reducers),
      EffectsModule.forRoot([
        AuthEffects,
        TestManagementEffects,
        QuestionRepositoryEffects,
        MatieresEffects,
      ]),
      StoreDevtoolsModule.instrument({
        maxAge: 25,
        logOnly: environment.production,
      })
    ),
    provideAnimationsAsync(),
    {
      provide: HTTP_INTERCEPTORS,
      useClass: TokenInterceptor,
      multi: true,
    },
    {
      provide: DATE_PIPE_DEFAULT_OPTIONS,
      useValue: { dateFormat: 'dd/MM/yyyy' },
    },

    provideRouter(routes, withComponentInputBinding()),
    provideHttpClient(withInterceptorsFromDi()),
  ],
}).catch((err) => console.error(err));
