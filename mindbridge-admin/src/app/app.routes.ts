import { Routes } from '@angular/router';
import { LoginComponent } from './features/auth/login/login.component';
import { MainComponent } from './features/layouts/main/main.component';
import { DashboardComponent } from './features/dashboard/dashboard/dashboard.component';
import { ListUsersComponent } from './features/users/components/list-users/list-users.component';
import { ListRolesComponent } from './features/roles/components/list-roles/list-roles.component';
import { ListTestsComponent } from './features/test-management/components/liste-tests/list-tests.component';
import { AddTestComponent } from './features/test-management/components/add-test/add-test.component';
import { ScoringRulesManagementComponent } from './features/test-management/components/scoring-rules-management/scoring-rules-management.component';
import { AuthGuard } from './core/guard/auth.guard';
import { ListQuestionsRepositoryComponent } from './features/questions-repository/components/list-questions-repository/list-questions-repository.component';
import { ListMatieresComponent } from './features/matieres/components/liste-matieres/list-matieres.component';
import { AddContenueComponent } from './features/matieres/components/add-contenue/add-contenue.component';
import { ListContenuesComponent } from './features/matieres/components/list-contenues/list-contenues.component';
import { TestsSanteMentaleComponent } from './features/sante-mentale/components/tests-sante-mentale/tests-sante-mentale.component';
import { TestCategoriesComponent } from './features/sante-mentale/components/test-categories/test-categories.component';
import { MentalHealthRiskDashboardComponent } from './features/sante-mentale/components/mental-health-risk-dashboard/mental-health-risk-dashboard.component';
import { ListeEtudiantsComponent } from './features/etudiants/components/liste-etudiants/liste-etudiants.component';
import { DetailsEtudiantComponent } from './features/etudiants/components/details-etudiant/details-etudiant.component';
import { ListObservationsComponent } from './features/observations/components/list-observations/list-observations.component';

export const routes: Routes = [
  {
    path: 'login', // login
    component: LoginComponent,
  },
  {
    path: '',
    component: MainComponent,
    canActivate: [AuthGuard],
    children: [
      {
        path: '',
        redirectTo: '/dashboard',
        pathMatch: 'full',
      },
      {
        path: 'dashboard',
        component: DashboardComponent,
      },
      {
        path: 'users',
        component: ListUsersComponent,
      },
      {
        path: 'role-permission',
        component: ListRolesComponent,
      },
      {
        path: 'tests',
        component: ListTestsComponent,
      },
      {
        path: 'add-test',
        component: AddTestComponent,
      },
      { path: 'add-test/:id', component: AddTestComponent },
      {
        path: 'scoring-rules',
        component: ScoringRulesManagementComponent,
      },
      {
        path: 'questions-repository',
        component: ListQuestionsRepositoryComponent,
      },
      {
        path: 'matieres',
        component: ListMatieresComponent,
      },
      {
        path: 'add-contenue/:matiere',
        component: AddContenueComponent,
      },
      {
        path: 'list-contenues/:matiereId',
        component: ListContenuesComponent,
      },
      {
        path: 'sante-mentale',
        component: TestsSanteMentaleComponent,
      },
      {
        path: 'sante-mentale/risk-dashboard',
        component: MentalHealthRiskDashboardComponent,
      },
      {
        path: 'sante-mentale/test-categories',
        component: TestCategoriesComponent,
      },
      {
        path: 'etudiants',
        component: ListeEtudiantsComponent,
      },
      {
        path: 'etudiant/:id',
        component: DetailsEtudiantComponent,
      },
      {
        path: 'observations',
        component: ListObservationsComponent,
      },
    ],
  },
  {
    path: '**',
    redirectTo: 'login',
  },
];
