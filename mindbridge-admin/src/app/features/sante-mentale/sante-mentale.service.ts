import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { CategoryTestsResponse } from './models/category-tests-response.model';
import { IEtudiantSearchForm } from '../etudiants/models/etudiant-search-request';
import { getQuery, Pagination } from '../shared/helpers/query.helper';
import { ApiResponse } from '../../core/models/api-response';
import { EtudiantResponse } from '../etudiants/models/etudiant-response';
import { IParticipantSearchForm } from './models/particicpant-search-request';
import { ParticipantResponse } from './models/participant-response';
import { StudentTestHistoryResponse } from './models/student-test-history';

@Injectable({
  providedIn: 'root',
})
export class SanteMentaleService {
  private http = inject(HttpClient);

  getCategoryTests(category_id: number): Observable<CategoryTestsResponse> {
    const url = environment.BASE_URL_API +
      'mind_bridge/categories/' +
      category_id +
      '/tests';
    console.log('[SanteMentaleService] Fetching from URL:', url);
    console.log('[SanteMentaleService] Category ID:', category_id);
    return this.http.get<CategoryTestsResponse>(url).pipe(
      tap((response) => {
        console.log('[SanteMentaleService] Response received:', response);
      }),
      catchError((error) => {
        console.error('[SanteMentaleService] HTTP Error:', error);
        console.error('[SanteMentaleService] Error status:', error.status);
        console.error('[SanteMentaleService] Error statusText:', error.statusText);
        throw error;
      })
    );
  }

  /**
   * Get all tests from a parent category and all its children categories
   * This endpoint returns all tests in one list
   *
   * @param category_id - Parent category ID (e.g., 2 for Sante Mentale)
   * @returns Observable with all tests from parent and child categories
   */
  getAllTestsByParentCategory(category_id: number): Observable<CategoryTestsResponse> {
    const url = environment.BASE_URL_API +
      'mind_bridge/categories/' +
      category_id +
      '/all-tests';
    console.log('[SanteMentaleService] Fetching all tests from URL:', url);
    console.log('[SanteMentaleService] Parent Category ID:', category_id);
    return this.http.get<CategoryTestsResponse>(url).pipe(
      tap((response) => {
        console.log('[SanteMentaleService] All tests response received:', response);
      }),
      catchError((error) => {
        console.error('[SanteMentaleService] HTTP Error:', error);
        console.error('[SanteMentaleService] Error status:', error.status);
        console.error('[SanteMentaleService] Error statusText:', error.statusText);
        throw error;
      })
    );
  }

  getListeParticipants(
    searchForm: IParticipantSearchForm,
    pagination: Pagination
  ): Observable<ApiResponse<ParticipantResponse>> {
    const params = getQuery(searchForm, pagination);

    return this.http.get<ApiResponse<ParticipantResponse>>(
      environment.BASE_URL_API + 'mind_bridge/mental-health-tests/participants',
      { params }
    );
  }

  /**
   * Get student mental health test history with all details
   * @param etudiantId - Student ID
   * @returns Observable with student test history including scores, observations, and scoring details
   */
  getStudentTestHistory(etudiantId: number): Observable<StudentTestHistoryResponse> {
    const url = environment.BASE_URL_API + `mind_bridge/etudiant/${etudiantId}/test-history`;
    return this.http.get<StudentTestHistoryResponse>(url).pipe(
      tap((response) => {
        console.log('[SanteMentaleService] Student test history received:', response);
      }),
      catchError((error) => {
        console.error('[SanteMentaleService] Error fetching test history:', error);
        throw error;
      })
    );
  }
}
