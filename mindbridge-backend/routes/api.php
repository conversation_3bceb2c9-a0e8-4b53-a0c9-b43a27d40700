<?php

use App\Http\Controllers\MindBridge\MindBridgeNotesController;
use App\Http\Controllers\MindBridge\MindBridgeTestController;
use App\Http\Controllers\MindBridge\TestScoringRuleController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\CentraleNiveauMatierController;
use App\Http\Controllers\CommentsController;
use App\Http\Controllers\MindBridge\ChaperController;
use App\Http\Controllers\MindBridge\ContentController;
use App\Http\Controllers\MindBridge\MindBridgeEtudiantController;
use App\Http\Controllers\MindBridge\OptionController;
use App\Http\Controllers\MindBridge\QuestionController;
use App\Http\Controllers\MindBridge\StepController;
use App\Http\Controllers\MindBridge\HootyIAController;
use App\Http\Controllers\MindBridge\CategoryController;
use App\Http\Controllers\MindBridge\ObservationController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/


// UNAUTHORIZED
Route::get('unauthorized', function () {
    return response()->json(['message' => 'Authentication Required!'], 401);
})->name('unauthorized');


Route::group(['prefix' => "comments"], function () {
    Route::post('add_comment', [CommentsController::class, 'store']);
});

Route::group(['prefix' => "mind_bridge"], function () {
    Route::middleware(['auth:sanctum'])->group(function () {
        Route::get('tests/{id}', [MindBridgeTestController::class, 'show']);
        Route::get('tests/{id}/step', [MindBridgeTestController::class, 'getTestFirsTStep']);
        Route::post('tests/storeAll', [MindBridgeTestController::class, 'storeAll']);
        Route::put('tests/updateAll/{id}', [MindBridgeTestController::class, 'updateAll']);
        Route::delete('tests/{id}', [MindBridgeTestController::class, 'destroy']);
        Route::apiResource('tests', MindBridgeTestController::class);
        Route::post('/run_test', [MindBridgeTestController::class, 'runMentalHealthTestBySubCategory']);

        // Scoring Rules Routes
        Route::get('tests/{testId}/scoring-rules', [TestScoringRuleController::class, 'index']);
        Route::post('tests/{testId}/scoring-rules', [TestScoringRuleController::class, 'store']);
        Route::put('tests/{testId}/scoring-rules/{ruleId}', [TestScoringRuleController::class, 'update']);
        Route::delete('tests/{testId}/scoring-rules/{ruleId}', [TestScoringRuleController::class, 'destroy']);

        Route::get('categories/board', [MindBridgeTestController::class, 'testBoardCategories']);
        Route::get('categories/{id}/tests', [MindBridgeTestController::class, 'getTestByCategoryId']);
        Route::get('categories/{id}/all-tests', [MindBridgeTestController::class, 'getAllTestsByParentCategory']);
        Route::get('students/category/{id}', [MindBridgeTestController::class, 'getStudentsWithTestsByCategoryId']);
        Route::get('mental-health-tests/participants', [MindBridgeTestController::class, 'getParticipantsMenalHealth']);
        Route::get('mental-health-risk-dashboard', [MindBridgeTestController::class, 'getMentalHealthRiskDashboard']);
        Route::get('/subcategories', [CategoryController::class, 'getSubcategories']);

        Route::get('options/{id}', [OptionController::class, 'show']);
        Route::put('options/{id}', [OptionController::class, 'update']);
        Route::delete('options/{id}', [OptionController::class, 'destroy']);
        Route::apiResource('options', OptionController::class);

        Route::post('questions', [QuestionController::class, 'store']);
        Route::get('questions/{id}', [QuestionController::class, 'show']);
        Route::post('questions/{id}', [QuestionController::class, 'update']);
        Route::delete('questions/{id}', [QuestionController::class, 'destroy']);
        Route::get('questions', [QuestionController::class, 'index']);
        Route::get('questions/category/{id}', [QuestionController::class, 'getQuestionsByCategory']);
        Route::post('questions/upload-image', [QuestionController::class, 'uploadImage']);
        Route::post('questions/delete-image', [QuestionController::class, 'deleteImage']);

        Route::get('niveaux', [CentraleNiveauMatierController::class, 'listNiveaux']);
        Route::get('niveaux/matiere/{id}', [CentraleNiveauMatierController::class, 'niveauxByMatiere']);
        Route::get('matieres', [CentraleNiveauMatierController::class, 'listMatieres']);
        Route::get('matieres/niveau/{id}', [CentraleNiveauMatierController::class, 'matieresByNiveau']);
        Route::get('subjects', [CentraleNiveauMatierController::class, 'getSubjects']);
        Route::get('levels', [CentraleNiveauMatierController::class, 'getLevels']);



        Route::get('steps/{id}', [StepController::class, 'show']);
        Route::put('steps/{id}', [StepController::class, 'update']);

        // Observations routes
        Route::get('observations', [ObservationController::class, 'index']);
        Route::post('observations', [ObservationController::class, 'store']);
        Route::get('observations/{id}', [ObservationController::class, 'show']);
        Route::put('observations/{id}', [ObservationController::class, 'update']);
        Route::delete('observations/{id}', [ObservationController::class, 'destroy']);
        Route::get('observations/{id}/test', [ObservationController::class, 'getMappedTest']);
        Route::get('by-trigger-type', [ObservationController::class, 'getByTriggerType']);
        Route::delete('steps/{id}', [StepController::class, 'destroy']);
        Route::apiResource('steps', StepController::class);
        Route::post('steps/next-step', [StepController::class,'getNextStep']);
        
        Route::post('chapters', [ChaperController::class, 'store']);
        Route::get('chapters/niveau-matiere', [ChaperController::class, 'byNiveauMatiere']);
        Route::get('chapters/{id}', [ChaperController::class, 'show']);
        Route::put('chapters/{id}', [ChaperController::class, 'update']);
        Route::delete('chapters/{id}', [ChaperController::class, 'destroy']);
        Route::get('chapters', [ChaperController::class, 'index']);

        Route::post('contents', [ContentController::class, 'store']);
        Route::get('contents/{id}', [ContentController::class, 'show']);
        Route::post('contents/{id}', [ContentController::class, 'update']);
        Route::delete('contents/{id}', [ContentController::class, 'destroy']);
        Route::get('contents', [ContentController::class, 'index']);
        Route::get('matiere-contents', [ContentController::class, 'byMatiere']);
        Route::post('contents-by-niveau-matiere', [ContentController::class, 'byNiveauMatiere']);

        
        Route::get('etudiants/list', [MindBridgeEtudiantController::class, 'index']);
        Route::get('school/list', [MindBridgeEtudiantController::class, 'schoolListe']);
        Route::get('etudiant/detail/{id}', [MindBridgeEtudiantController::class, 'getEtudiantInfoBo']);
        Route::get('etudiant/{id}/test-history', [MindBridgeTestController::class, 'getStudentTestHistory']);

        Route::post('assign-test/{etudiantId}/{testId}', [MindBridgeTestController::class, 'assignTestToEtudiant']);

        Route::get('notes', [MindBridgeNotesController::class, 'index']);
        Route::post('notes', [MindBridgeNotesController::class, 'importNotesFromCsv']);
        
    });

    Route::group(['prefix' => 'auth'], function () {
        Route::post('login', [AuthController::class, 'mindberdgeLogin']);
        Route::post('/etudiant/login', [AuthController::class, 'loginEtudiant']);
    });
    

    // work here
    Route::group(['prefix' => 'etudiant'], function () {
        Route::middleware(['auth:sanctum', 'update.connection.points'])->group(function () {
            Route::get('categories/board', [MindBridgeTestController::class, 'testBoardCategoriesMo']);


            Route::get('/tests', [MindBridgeEtudiantController::class, 'etudiantTestProfiling']);
            Route::post('/tests', [MindBridgeEtudiantController::class, 'etudiantTestStore']);


            Route::get('/matiers/{id}', [MindBridgeEtudiantController::class, 'getMatiersByCategory']);

            Route::get('/contents', [MindBridgeEtudiantController::class, 'getLessonByMatier']);
            Route::get('/contents/test', [MindBridgeEtudiantController::class, 'getTestByLesson']);

            Route::get('/infos/{id}', [MindBridgeEtudiantController::class, 'getEtudiantInfos']);

            Route::get('/tests/hebdomadaire', [MindBridgeTestController::class, 'latestHebdomadaireTest']);
            Route::get('/tests/recommandation', [MindBridgeTestController::class, 'recommandationTest']);
            Route::post('/tests/recommandation', [MindBridgeTestController::class, 'saveRecommandationResult']);


            Route::post('hooty/store', [HootyIAController::class, 'store']);
            Route::get('hooty/get', [HootyIAController::class, 'index']);
            Route::get('hooty/{id}', [HootyIAController::class, 'get']);
        });
    });
});
