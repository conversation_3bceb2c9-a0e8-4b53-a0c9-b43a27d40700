<?php

namespace App\Http\Controllers\MindBridge;

use App\Http\Controllers\Controller;
use App\Models\MindBridge\Observation;
use App\Models\MindBridge\Test;
use Illuminate\Http\Request;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Exception;

class ObservationController extends Controller
{
    /**
     * Display a listing of observations with pagination
     */
    public function index(Request $request)
    {
        try {
            $search = $request->query('search', '');
            $category = $request->query('category', '');
            $visibleTo = $request->query('visible_to', '');
            $active = $request->query('active', '');
            $limit = $request->query('limit', 10);
            $page = $request->query('page', 1);

            $query = Observation::query();

            if ($search) {
                $query->where('label', 'like', '%' . $search . '%')
                    ->orWhere('description', 'like', '%' . $search . '%');
            }

            if ($category) {
                $query->where('category', $category);
            }

            if ($visibleTo) {
                $query->where('visible_to', $visibleTo);
            }

            if ($active !== '') {
                $query->where('active', (bool) $active);
            }

            $total = $query->count();
            $observations = $query->paginate($limit, ['*'], 'page', $page);

            return response()->json([
                'success' => true,
                'data' => $observations->items(),
                'current_page' => $observations->currentPage(),
                'per_page' => $observations->perPage(),
                'total_pages' => $observations->lastPage(),
                'total_items' => $total,
            ], 200);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fetching observations: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Store a newly created observation
     */
    public function store(Request $request)
    {
        try {
            $validated = $request->validate([
                'label' => 'required|string|max:255',
                'category' => 'nullable|string|max:255',
                'visible_to' => 'required|in:teacher,parent,both',
                'description' => 'nullable|string',
                'active' => 'boolean',
                'test_id' => 'nullable',
                'trigger_type' => 'required|in:parent,teacher,simulated_grade_shutdown',
            ]);

            $observation = Observation::create($validated);

            return response()->json([
                'success' => true,
                'message' => 'Observation created successfully',
                'data' => $observation,
            ], 201);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $e->errors(),
            ], 422);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error creating observation: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Display the specified observation
     */
    public function show($id)
    {
        try {
            $observation = Observation::with('test')->findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => $observation,
            ], 200);
        } catch (ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Observation not found',
            ], 404);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fetching observation: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update the specified observation
     */
    public function update(Request $request, $id)
    {
        try {
            $observation = Observation::findOrFail($id);

            $validated = $request->validate([
                'label' => 'sometimes|required|string|max:255',
                'category' => 'nullable|string|max:255',
                'visible_to' => 'sometimes|required|in:teacher,parent,both',
                'description' => 'nullable|string',
                'active' => 'boolean',
                'test_id' => 'nullable|integer|exists:tests,id',
                'trigger_type' => 'sometimes|required|in:parent,teacher,simulated_grade_shutdown',
            ]);

            $observation->update($validated);

            return response()->json([
                'success' => true,
                'message' => 'Observation updated successfully',
                'data' => $observation,
            ], 200);
        } catch (ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Observation not found',
            ], 404);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $e->errors(),
            ], 422);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating observation: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Delete the specified observation
     */
    public function destroy($id)
    {
        try {
            $observation = Observation::findOrFail($id);
            $observation->delete();

            return response()->json([
                'success' => true,
                'message' => 'Observation deleted successfully',
            ], 200);
        } catch (ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Observation not found',
            ], 404);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error deleting observation: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get test mapped to an observation
     */
    public function getMappedTest($id)
    {
        try {
            $observation = Observation::findOrFail($id);
            $test = $observation->test;

            return response()->json([
                'success' => true,
                'data' => $test,
            ], 200);
        } catch (ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Observation not found',
            ], 404);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fetching mapped test: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get observations by trigger type
     */
    public function getByTriggerType(Request $request)
    {
        try {
            $visibleTo = $request->query('visible_to', '');


            $query = Observation::where('active', true);

            if ($visibleTo) {
                $query->where(function ($q) use ($visibleTo) {
                    $q->where('visible_to', $visibleTo)
                        ->orWhere('visible_to', 'both');
                });
            }

            $observations = $query->get();

            return response()->json([
                'success' => true,
                'data' => $observations,
            ], 200);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fetching observations: ' . $e->getMessage(),
            ], 500);
        }
    }
}

