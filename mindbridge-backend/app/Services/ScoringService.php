<?php

namespace App\Services;

use App\Models\MindBridge\Option;
use App\Models\MindBridge\TestScoringRule;

class ScoringService
{
    /**
     * Calculate the overall test score
     */
    public function calculateTestScore($test, $answers)
    {
        $score = 0;
        $maxScore = 0;

        // Get all steps for this test
        $steps = $test->steps()->with('question', 'question.options')->get();

        foreach ($steps as $step) {
            $pointsValue = $step->points_value ?? 1;
            $maxScore += $pointsValue;

            if (isset($answers[$step->question_id])) {
                if ($this->isAnswerCorrect($step, $answers[$step->question_id])) {
                    $score += $pointsValue;
                }
            }
        }

        // Calculate overall percentage
        $percentage = ($maxScore > 0) ?
            round(($score / $maxScore) * 100, 2) : 0;

        // Find applicable scoring rule
        $rule = $this->findApplicableRule($test, $percentage);

        return [
            'total_score' => $score,
            'total_max_score' => $maxScore,
            'percentage' => $percentage,
            'feedback' => $rule ? $rule->feedback : null,
            'recommendation' => $rule ? $rule->recommendation : null,
            'interpretation' => $rule ? $rule->interpretation : 'À évaluer',
        ];
    }

    /**
     * Check if an answer is correct
     */
    private function isAnswerCorrect($step, $selectedOptions)
    {
        // Get all correct options for this question
        $correctOptions = Option::where('question_id', $step->question_id)
            ->where('isCorrect', true)
            ->pluck('id')
            ->map(fn($id) => (int)$id)
            ->toArray();

        // Normalize selected options to array of integers
        $selected = is_array($selectedOptions) ?
            array_map('intval', $selectedOptions) : [(int)$selectedOptions];

        // If no correct options defined, answer is wrong
        if (empty($correctOptions)) {
            return false;
        }

        // Check if all correct options are selected and no wrong options
        $intersect = array_intersect($selected, $correctOptions);
        return count($intersect) === count($correctOptions) && count($intersect) === count($selected);
    }

    /**
     * Find the applicable scoring rule for a test and percentage
     */
    private function findApplicableRule($test, $percentage)
    {
        return TestScoringRule::where('test_id', $test->id)
            ->where('min_percentage', '<=', $percentage)
            ->where('max_percentage', '>=', $percentage)
            ->first();
    }
}

