import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mindbridge/app/routes.dart';
import 'package:mindbridge/core/constants/app_strings.dart';
import 'package:mindbridge/core/utilities/extensions.dart';
import 'package:mindbridge/data/providers/local/secure_storage_service.dart';
import 'package:mindbridge/presentation/widgets/global_loader.dart';
import 'package:sizer/sizer.dart';

import '../../../../core/models/category.dart' as cat;
import '../../app/theme/colors.dart';
import '../../core/utilities/app_images.dart';
import '../../core/utilities/loading_utils.dart';
import '../../data/providers/remote/dio_client.dart';
import '../../data/repositories/evaluation_repository.dart';
import 'global_dialog.dart';
import 'global_spots.dart';
import 'gradient_button.dart';



class TestBtmSheet {
  static void show(cat.ToDo test, {String? type }) {
    if (!Get.isBottomSheetOpen!) {
      Get.bottomSheet(
        Stack(
          children: [
            Positioned.fill(
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 6, sigmaY: 6),
                child: Container(
                  color: Colors.black.withOpacity(0.3),
                ),
              ),
            ),
            Align(
              alignment: Alignment.bottomCenter,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.vertical(
                    top: Radius.circular(2.h),
                  ),
                ),
                child: PopScope(
                  canPop: false,
                  child: _TestBottomSheet(test: test, type: type),
                ),
              ),
            ),
          ],
        ),
        isScrollControlled: true,
        enableDrag: false,
        isDismissible: false,
        backgroundColor: Colors.transparent,
      );
    }
  }
}

class _TestBottomSheet extends StatefulWidget {
  final cat.ToDo test;
  final String? type;

  const _TestBottomSheet({required this.test, required this.type});

  @override
  State<_TestBottomSheet> createState() => _TestBottomSheetState();
}

class _TestBottomSheetState extends State<_TestBottomSheet> {
  final RxBool _showIntro = true.obs;

  bool get isTest {
    final lowerType = widget.test.type?.toString().toLowerCase() ?? '';
    final lowerTitle = widget.test.title?.toLowerCase() ?? '';
    return lowerType.contains('test') || lowerTitle.contains('test');
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (_showIntro.value) {
        return _buildIntro(isTest, type: widget.type);
      } else {
        return _buildTestContent(widget.test);
      }
    });
  }

  Widget _buildIntro(bool isTest, { String? type }) {
    final isMentalHealth = type == 'mental_health_self_test_participated';
    final mainTitle = widget.test.title ??
        (isTest ? AppStrings.testIntroTitle : AppStrings.surveyIntroTitle);
    final description = widget.test.description ??
        (isTest
            ? AppStrings.testIntroDescription
            : AppStrings.surveyIntroDescription);
    final buttonText =
        isTest ? AppStrings.startTestButton : AppStrings.startSurveyButton;

    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4.w, horizontal: 4.w),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            isMentalHealth ? AppStrings.mentalHealthTitle : mainTitle,
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 2.h),
          Text(
            isMentalHealth ? AppStrings.mentalHealthDescription : description,
            style: TextStyle(
              fontSize: 12.sp,
              color: AppColors.secondary,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 3.h),
          GradientButton(
            text: isMentalHealth ? AppStrings.mentalHealthButton : buttonText,
            onPressed: () {
              print("Start test");
              _showIntro.value = false;
            },
          ),
        ],
      ),
    );
  }

  Widget _buildTestContent(cat.ToDo test) {
    final RxInt currentStep = 1.obs;
    final RxMap<int, List<int>> selectedAnswers = <int, List<int>>{}.obs;

    List<cat.Step> steps = test.steps ?? [];

    return LayoutBuilder(
      builder: (ctx, constraints) {
        return SizedBox(
          height: 50.h,
          child: Stack(
            clipBehavior: Clip.none,
            children: [
              Positioned.fill(
                child: GlobalSpots(
                  width: constraints.maxWidth,
                  height: constraints.maxHeight,
                ),
              ),
              Padding(
                padding: EdgeInsets.symmetric(vertical: 4.w, horizontal: 4.w),
                child: Obx(() {
                  final currentQuestionObj =
                      steps.isNotEmpty && currentStep.value <= steps.length
                          ? steps[currentStep.value - 1]
                          : null;

                  final currentQuestion =
                      currentQuestionObj?.question?.content ?? '';
                  final currentImage = currentQuestionObj?.question?.imagePath;
                  final currentChoices =
                      currentQuestionObj?.question?.options ?? [];
                  final questionType = currentQuestionObj?.type ?? 'one';

                  return Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      LinearProgressIndicator(
                        value: currentStep.value / steps.length,
                        backgroundColor: AppColors.primary.withOpacity(0.2),
                        valueColor:
                            const AlwaysStoppedAnimation(AppColors.primary),
                      ),
                      SizedBox(height: 1.h),
                      Text(
                        'Question ${currentStep.value} sur ${steps.length}',
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.bold,
                          color: AppColors.secondary,
                        ),
                      ),
                      SizedBox(height: 2.h),
                      Text(
                        currentQuestion,
                        style: TextStyle(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      SizedBox(height: 2.h),
                      if (currentImage != null && currentImage.isNotEmpty) ...[
                        (currentImage as String).toImage(
                          height: 10.h,
                        ),
                        SizedBox(height: 2.h),
                      ],
                      Expanded(
                        child: questionType == 'true_false'
                            ? _buildTrueFalseOptions(
                                currentQuestionObj, selectedAnswers)
                            : ListView(
                                children: currentChoices.map((choice) {
                                  return Obx(() {
                                    final questionId =
                                        currentQuestionObj?.questionId ?? -1;
                                    final selected =
                                        selectedAnswers[questionId] ?? [];
                                    final isSelected =
                                        selected.contains(choice.id);

                                    return CheckboxListTile(
                                      value: isSelected,
                                      title: Row(
                                        children: [
                                          if (choice.icon != null) ...[
                                            choice.icon!.toIconText(),
                                            SizedBox(width: 2.w),
                                          ],
                                          Flexible(
                                            child: Text(choice.name ?? ''),
                                          ),
                                        ],
                                      ),
                                      onChanged: (checked) {
                                        if (questionType == 'one') {
                                          selectedAnswers.update(
                                            questionId,
                                            (_) => [choice.id!],
                                            ifAbsent: () => [choice.id!],
                                          );
                                        } else if (questionType == 'many') {
                                          if (checked == true) {
                                            selectedAnswers.update(
                                              questionId,
                                              (list) => [...list, choice.id!],
                                              ifAbsent: () => [choice.id!],
                                            );
                                          } else {
                                            selectedAnswers.update(
                                              questionId,
                                              (list) => list..remove(choice.id),
                                            );
                                          }
                                        }
                                      },
                                    );
                                  });
                                }).toList(),
                              ),
                      ),
                      GradientButton(
                        text: currentStep.value == steps.length
                            ? AppStrings.finish
                            : AppStrings.next,
                        onPressed: _isButtonEnabled(
                                currentQuestionObj, selectedAnswers)
                            ? () {
                                if (currentStep.value < steps.length) {
                                  currentStep.value++;
                                } else {
                                  _submitTest(test, selectedAnswers, type: widget.type);
                                }
                              }
                            : null,
                      ),
                    ],
                  );
                }),
              )
            ],
          ),
        );
      },
    );
  }

  bool _isButtonEnabled(
      cat.Step? currentQuestionObj, RxMap<int, List<int>> selectedAnswers) {
    if (currentQuestionObj == null) return false;

    final questionId = currentQuestionObj.questionId ?? -1;
    final selected = selectedAnswers[questionId] ?? [];

    return selected.isNotEmpty;
  }

  Widget _buildTrueFalseOptions(
      cat.Step? currentQuestionObj, RxMap<int, List<int>> selectedAnswers) {
    final questionId = currentQuestionObj?.questionId ?? -1;
    final selected = selectedAnswers[questionId] ?? [];
    final isTrueSelected = selected.contains(1);
    final isFalseSelected = selected.contains(0);

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        _buildTrueFalseOption(
          text: "Vrai",
          isSelected: isTrueSelected,
          onTap: () {
            selectedAnswers[questionId] = [1];
          },
        ),
        _buildTrueFalseOption(
          text: "Faux",
          isSelected: isFalseSelected,
          onTap: () {
            selectedAnswers[questionId] = [0];
          },
        ),
      ],
    );
  }

  Widget _buildTrueFalseOption({
    required String text,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 1.h, horizontal: 4.w),
        decoration: BoxDecoration(
          color: isSelected
              ? AppColors.primary.withOpacity(0.2)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(0.5.h),
        ),
        child: Row(
          children: [
            Icon(
              text == "Vrai" ? Icons.check : Icons.close,
              color: text == "Vrai" ? Colors.green : Colors.red,
            ),
            SizedBox(width: 2.w),
            Text(
              text,
              style: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.bold,
                color: text == "Vrai" ? Colors.green : Colors.red,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _submitTest(
      cat.ToDo test, RxMap<int, List<int>> selectedAnswers, {String? type}) async {
    final payload = {
      "test_id": test.id,
      'action_test_type': type ?? 'test',
      "data": selectedAnswers.entries.map((entry) {
        final question = test.steps
            ?.firstWhere(
              (step) => step.questionId == entry.key,
              orElse: () => cat.Step(),
            )
            .question;

        final isTrue = question?.isTrue == true;
        final isFalse = question?.isFalse == true;

        if (isTrue) {
          return {
            "question_id": entry.key,
            "selected_options": ["is_true"],
          };
        } else if (isFalse) {
          return {
            "question_id": entry.key,
            "selected_options": ["is_false"],
          };
        }

        return {
          "question_id": entry.key,
          "selected_options": entry.value,
        };
      }).toList(),
    };
    if (!Get.isRegistered<EvaluationRepository>()) {
      Get.put(EvaluationRepository(dioClient: Get.find<DioClient>()));
    }
    if (!Get.isRegistered<SecureStorageService>()) {
      Get.put(SecureStorageService());
    }

    Get.dialog(
      const PopScope(canPop: false, child: Center(child: GlobalLoader())),
      barrierDismissible: false,
    );

    try {
      final response =
          await Get.find<EvaluationRepository>().submitEvaluation(payload);
      
      final secureStorage = Get.find<SecureStorageService>();

      Get.back();

      if (response != null && response.containsKey("feedback_message")) {
        final feedbackMessage =
            response["feedback_message"] ?? AppStrings.successMessage;

        GlobalDialog.showTheDialog(
          iconTap: () => Get.offAllNamed(Routes.btmNavigationBar),
          imagePath: AppImages.bigOwl,
          title: feedbackMessage,
          subtitle: '',
          buttonText: AppStrings.continuer,
          onButtonTap: () => Get.offAllNamed(Routes.btmNavigationBar),
        );
        await secureStorage.write('has_mental_health_test', '');
      }
    } catch (e) {
      Get.back();

      debugPrint("Error submitting test: $e");
      LoadingUtils.showError(AppStrings.errorMessage);
    }
  }
}
